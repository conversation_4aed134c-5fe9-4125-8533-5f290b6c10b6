# Arquivos de ambiente e credenciais
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Ambientes virtuais
venv/
env/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/
*.iml
*.iws

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# Logs
*.log
*.tmp
logs/

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Arquivos da extração Docker (podem ser removidos após setup)
filesystem/
extracted-image/
container-content/
*.tar

# Node.js (se houver frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Arquivos temporários
tmp/
temp/
.tmp/

# Arquivos de backup
*.bak
*.backup
*~
