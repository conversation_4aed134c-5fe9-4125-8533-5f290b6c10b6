#!/usr/bin/env python3
"""
Script para testar as credenciais AWS e acesso ao Secrets Manager.
Execute este script para verificar se tudo está configurado corretamente.
"""

import boto3
import json
import os
from botocore.exceptions import ClientError, NoCredentialsError

def test_aws_credentials():
    """Testa se as credenciais AWS estão configuradas corretamente."""
    print("🔍 Testando credenciais AWS...")
    
    try:
        # Tenta criar um cliente STS para verificar credenciais
        sts_client = boto3.client('sts')
        identity = sts_client.get_caller_identity()
        
        print("✅ Credenciais AWS válidas!")
        print(f"   Account ID: {identity.get('Account')}")
        print(f"   User/Role ARN: {identity.get('Arn')}")
        return True
        
    except NoCredentialsError:
        print("❌ Credenciais AWS não encontradas!")
        print("   Configure usando 'aws configure' ou IAM Role para EC2")
        return False
        
    except Exception as e:
        print(f"❌ Erro ao verificar credenciais AWS: {e}")
        return False

def test_secrets_manager_access():
    """Testa o acesso ao AWS Secrets Manager."""
    print("\n🔍 Testando acesso ao Secrets Manager...")
    
    secret_name = os.getenv("AWS_SECRET_NAME", "prod/audie")
    region_name = os.getenv("AWS_REGION", "us-east-1")
    
    try:
        client = boto3.client("secretsmanager", region_name=region_name)
        
        # Tenta listar secrets para verificar permissões básicas
        try:
            client.list_secrets(MaxResults=1)
            print("✅ Acesso ao Secrets Manager confirmado!")
        except ClientError as e:
            if e.response['Error']['Code'] == 'AccessDenied':
                print("⚠️  Acesso limitado ao Secrets Manager (pode ser normal)")
            else:
                raise
        
        # Tenta acessar o secret específico
        try:
            response = client.get_secret_value(SecretId=secret_name)
            secrets = json.loads(response["SecretString"])
            
            print(f"✅ Secret '{secret_name}' acessado com sucesso!")
            print(f"   Chaves encontradas: {list(secrets.keys())}")
            
            # Verifica se a chave OpenAI está presente
            if "OPENAI_API_KEY" in secrets:
                openai_key = secrets["OPENAI_API_KEY"]
                if openai_key and len(openai_key) > 10:
                    print(f"✅ OPENAI_API_KEY encontrada (***{openai_key[-4:]})")
                else:
                    print("⚠️  OPENAI_API_KEY está vazia ou muito curta")
            else:
                print("⚠️  OPENAI_API_KEY não encontrada no secret")
                
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ResourceNotFoundException':
                print(f"❌ Secret '{secret_name}' não encontrado!")
                print(f"   Verifique se o secret existe na região {region_name}")
            elif error_code == 'AccessDenied':
                print(f"❌ Sem permissão para acessar o secret '{secret_name}'")
                print("   Verifique as políticas IAM")
            else:
                print(f"❌ Erro ao acessar secret: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao conectar com Secrets Manager: {e}")
        return False

def test_config_loading():
    """Testa o carregamento da configuração do projeto."""
    print("\n🔍 Testando carregamento da configuração...")
    
    try:
        from src.config import Config
        
        print("✅ Módulo config importado com sucesso!")
        
        # Verifica algumas configurações importantes
        configs_to_check = [
            ("OPENAI_API_KEY", Config.OPENAI_API_KEY),
            ("MYSQL_HOST", Config.MYSQL_HOST),
            ("MYSQL_DB", Config.MYSQL_DB),
        ]
        
        for name, value in configs_to_check:
            if value:
                if "KEY" in name:
                    print(f"✅ {name}: ***{str(value)[-4:] if len(str(value)) > 4 else '***'}")
                else:
                    print(f"✅ {name}: {value}")
            else:
                print(f"⚠️  {name}: não configurado")
                
        return True
        
    except Exception as e:
        print(f"❌ Erro ao importar configuração: {e}")
        return False

def main():
    """Executa todos os testes."""
    print("🚀 Testando configuração AWS para audie-generator-ms\n")
    
    # Mostra informações do ambiente
    print("📋 Informações do ambiente:")
    print(f"   AWS_SECRET_NAME: {os.getenv('AWS_SECRET_NAME', 'prod/audie (padrão)')}")
    print(f"   AWS_REGION: {os.getenv('AWS_REGION', 'us-east-1 (padrão)')}")
    print(f"   AWS_PROFILE: {os.getenv('AWS_PROFILE', 'default (padrão)')}")
    print()
    
    # Executa os testes
    tests = [
        test_aws_credentials,
        test_secrets_manager_access,
        test_config_loading,
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # Resumo final
    print("\n" + "="*50)
    print("📊 RESUMO DOS TESTES:")
    
    if all(results):
        print("🎉 Todos os testes passaram! Configuração está correta.")
    else:
        print("⚠️  Alguns testes falharam. Verifique a configuração.")
        print("\n💡 Dicas:")
        print("   - Para EC2: Anexe uma IAM Role à instância")
        print("   - Para desenvolvimento: Execute 'aws configure'")
        print("   - Verifique se o secret 'prod/audie' existe no Secrets Manager")

if __name__ == "__main__":
    main()
