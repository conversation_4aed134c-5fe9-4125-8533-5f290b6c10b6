# Arquivo de exemplo para variáveis de ambiente
# Copie este arquivo para .env e ajuste os valores conforme necessário

# =============================================================================
# CONFIGURAÇÕES DE BANCO DE DADOS
# =============================================================================
MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com
MYSQL_DB=audit_gold
MYSQL_USER=devdb
MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J
MYSQL_PORT=3306

# =============================================================================
# CHAVES DE API - ⚠️ MANTER SEGURAS ⚠️
# =============================================================================
VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8
OPENAI_API_KEY=********************************************************************************************************************************************************************
DEEPSEEK_API_KEY=***********************************

# =============================================================================
# CONFIGURAÇÕES DA APLICAÇÃO
# =============================================================================
MODEL_SQL=gpt-4.1-mini
HOME=/tmp

# =============================================================================
# CONFIGURAÇÕES AWS LAMBDA (para compatibilidade)
# =============================================================================
LAMBDA_TASK_ROOT=/var/task
LAMBDA_RUNTIME_DIR=/var/runtime
LD_LIBRARY_PATH=/var/lang/lib:/lib64:/usr/lib64:/var/runtime:/var/runtime/lib:/var/task:/var/task/lib:/opt/lib
PATH=/var/lang/bin:/usr/local/bin:/usr/bin/:/bin:/opt/bin
LANG=en_US.UTF-8
TZ=:/etc/localtime

# =============================================================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO (opcional)
# =============================================================================
# DEBUG=true
# LOG_LEVEL=DEBUG
# ENVIRONMENT=development
