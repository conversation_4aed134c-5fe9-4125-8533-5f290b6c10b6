#!/usr/bin/env python3
"""
Script para testar a configuração AWS Lambda com Secrets Manager.
Simula o ambiente Lambda para verificar se tudo está funcionando.
"""

import os
import sys
import json

def simulate_lambda_environment():
    """Simula as variáveis de ambiente do Lambda."""
    print("🔧 Configurando ambiente Lambda simulado...")
    
    # Define as variáveis como seriam no Lambda
    os.environ["AWS_SCRT_NAME"] = "prod/audie"
    os.environ["AWS_RGN"] = "us-east-1"
    
    # Simula outras variáveis típicas do Lambda
    os.environ["AWS_LAMBDA_FUNCTION_NAME"] = "audie-generator-ms"
    os.environ["AWS_LAMBDA_FUNCTION_VERSION"] = "$LATEST"
    
    print("   ✅ Variáveis de ambiente configuradas:")
    print(f"      AWS_SCRT_NAME: {os.getenv('AWS_SCRT_NAME')}")
    print(f"      AWS_RGN: {os.getenv('AWS_RGN')}")

def test_secrets_loading():
    """Testa o carregamento de secrets."""
    print("\n🔍 Testando carregamento de secrets...")
    
    try:
        # Adiciona o path do projeto
        sys.path.insert(0, '/home/<USER>/Development/Web/softo/audie-generator-ms')
        
        from src.config import get_secret, Config
        
        # Testa a função get_secret diretamente
        secrets = get_secret()
        
        if secrets:
            print(f"   ✅ Secrets carregados: {list(secrets.keys())}")
            
            # Verifica chaves importantes
            important_keys = ["OPENAI_API_KEY", "DEEPSEEK_API_KEY"]
            for key in important_keys:
                if key in secrets:
                    value = secrets[key]
                    if value:
                        print(f"   ✅ {key}: ***{str(value)[-4:] if len(str(value)) > 4 else '***'}")
                    else:
                        print(f"   ⚠️  {key}: vazio (pode ser intencional)")
                else:
                    print(f"   ❌ {key}: não encontrado")
        else:
            print("   ⚠️  Nenhum secret carregado - usando fallback para env vars")
            
        # Testa a classe Config
        print(f"\n   📋 Configuração final:")
        print(f"      OPENAI_API_KEY: {'✅ definida' if Config.OPENAI_API_KEY else '❌ não definida'}")
        print(f"      DEEPSEEK_API_KEY: {'✅ definida' if Config.DEEPSEEK_API_KEY else '❌ não definida'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao carregar secrets: {e}")
        return False

def test_openai_service():
    """Testa o serviço OpenAI."""
    print("\n🔍 Testando OpenAI Service...")
    
    try:
        sys.path.insert(0, '/home/<USER>/Development/Web/softo/audie-generator-ms')
        
        from src.services.openai_service import OpenAIService
        from src.config import Config
        
        # Verifica se as chaves estão disponíveis
        if not Config.OPENAI_API_KEY:
            print("   ⚠️  OPENAI_API_KEY não está definida")
            
        if not Config.DEEPSEEK_API_KEY:
            print("   ⚠️  DEEPSEEK_API_KEY não está definida")
            
        # Tenta criar o serviço
        service = OpenAIService()
        print("   ✅ OpenAIService criado com sucesso")
        
        # Verifica se os clientes foram criados corretamente
        if hasattr(service, 'client'):
            print("   ✅ Cliente DeepSeek configurado")
        
        print("   ✅ Serviço pronto para uso")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao criar OpenAIService: {e}")
        return False

def lambda_handler_simulation():
    """Simula uma execução do Lambda handler."""
    print("\n🚀 Simulando execução Lambda...")
    
    try:
        # Simula um evento Lambda típico
        event = {
            "httpMethod": "POST",
            "path": "/test",
            "body": json.dumps({"message": "test"})
        }
        
        context = type('Context', (), {
            'function_name': 'audie-generator-ms',
            'function_version': '$LATEST',
            'invoked_function_arn': 'arn:aws:lambda:us-east-1:123456789012:function:audie-generator-ms',
            'memory_limit_in_mb': '512',
            'remaining_time_in_millis': lambda: 30000
        })()
        
        print(f"   📦 Event: {event['httpMethod']} {event['path']}")
        print(f"   ⚡ Context: {context.function_name}")
        print("   ✅ Simulação de Lambda executada com sucesso")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro na simulação Lambda: {e}")
        return False

def main():
    """Executa todos os testes para Lambda."""
    print("🚀 TESTE DE CONFIGURAÇÃO PARA AWS LAMBDA\n")
    
    # Executa os testes
    tests = [
        ("Ambiente Lambda", simulate_lambda_environment),
        ("Carregamento de Secrets", test_secrets_loading),
        ("OpenAI Service", test_openai_service),
        ("Simulação Lambda", lambda_handler_simulation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Erro no teste '{test_name}': {e}")
            results.append(False)
    
    # Resumo final
    print("\n" + "="*60)
    print("📊 RESUMO DOS TESTES LAMBDA:")
    
    if all(results):
        print("🎉 Todos os testes passaram! Configuração Lambda está correta.")
        print("\n✅ Próximos passos:")
        print("   1. Faça deploy da função Lambda")
        print("   2. Anexe a IAM Role com permissões do Secrets Manager")
        print("   3. Configure as variáveis de ambiente (AWS_SCRT_NAME, AWS_RGN)")
        print("   4. Teste em produção")
    else:
        print("⚠️  Alguns testes falharam. Verifique a configuração.")
        print("\n💡 Checklist para Lambda:")
        print("   ✓ IAM Role com permissões SecretsManager")
        print("   ✓ Secret 'prod/audie' existe e tem as chaves necessárias")
        print("   ✓ Variáveis de ambiente AWS_SCRT_NAME e AWS_RGN configuradas")
        print("   ✓ Função Lambda tem a role anexada")

if __name__ == "__main__":
    main()
