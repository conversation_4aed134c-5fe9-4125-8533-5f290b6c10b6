# Reconstrução do Dockerfile

Este documento explica como foi reconstruído o Dockerfile original a partir da inspeção da imagem Docker `audie-generator-ms`.

## Metodologia de Reconstrução

### 1. Extração do Conteúdo da Imagem
- Utilizamos `docker save` para extrair a imagem como arquivo tar
- Extraímos todas as camadas da imagem
- Reconstruímos o sistema de arquivos da imagem
- Copiamos o código fonte de `/var/task` para a pasta local

### 2. Análise da Configuração da Imagem
- Utilizamos `docker inspect` para obter metadados da imagem
- Utilizamos `docker history` para entender a sequência de comandos

### 3. Informações Extraídas

#### Imagem Base
- **Base**: `public.ecr.aws/lambda/python:3.12-arm64`
- **Arquitetura**: ARM64
- **Plataforma**: AWS Lambda

#### Estrutura de Arquivos
- **C<PERSON><PERSON> fonte**: `/var/task/src/`
- **Requirements**: `/var/task/tmp/requirements.txt`
- **Working Directory**: `/var/task`

#### Dependências Instaladas
1. `uvicorn[standard]` - Servidor ASGI
2. `vanna[openai,mysql]` - Framework para SQL com IA
3. Dependências do requirements.txt:
   - fastapi==0.115.1
   - pydantic==2.10.6
   - uvicorn==0.34.0
   - mangum==0.19.0
   - openai==1.68.2
   - SQLAlchemy==2.0.41

#### Variáveis de Ambiente
- **Banco de dados**: Configurações MySQL para AWS RDS
- **APIs**: Chaves para OpenAI, Vanna e DeepSeek
- **Lambda**: Configurações específicas do AWS Lambda
- **Modelo**: `MODEL_SQL=gpt-4.1-mini`

#### Configurações de Segurança
- **Usuário**: 1000 (não-root)
- **Ownership**: Arquivos pertencentes ao usuário 1000

#### Entrypoint e CMD
- **Entrypoint**: `/lambda-entrypoint.sh` (da imagem base)
- **CMD**: `["src.app.handler"]`

## Limitações da Reconstrução

### ❌ Não Recuperável
1. **Comentários originais** no Dockerfile
2. **Ordem exata** dos comandos (algumas otimizações podem ter sido feitas)
3. **Argumentos de build** específicos
4. **Versões exatas** de algumas dependências (se não especificadas)

### ⚠️ Informações Sensíveis Expostas
- **Chaves de API** estão visíveis nas variáveis de ambiente
- **Credenciais de banco** estão expostas
- **Recomendação**: Usar secrets ou variáveis de ambiente externas

### ✅ Recuperável com Alta Fidelidade
1. **Imagem base** e versão
2. **Dependências** e suas versões
3. **Estrutura de arquivos**
4. **Configurações de runtime**
5. **Variáveis de ambiente**
6. **Usuário e permissões**

## Uso do Dockerfile Reconstruído

```bash
# Build da imagem
docker build -f Dockerfile.reconstructed -t audie-generator-ms-rebuilt .

# Executar localmente (se necessário)
docker run -p 8080:8080 audie-generator-ms-rebuilt
```

## Melhorias Recomendadas

1. **Segurança**: Mover credenciais para variáveis de ambiente externas
2. **Multi-stage build**: Separar dependências de desenvolvimento
3. **Otimização**: Combinar comandos RUN para reduzir camadas
4. **Versionamento**: Fixar versões específicas das dependências

## Comparação com Original

O Dockerfile reconstruído deve produzir uma imagem funcionalmente idêntica ao original, mas pode diferir em:
- Ordem de alguns comandos
- Otimizações de cache
- Comentários e documentação
- Estrutura de camadas (se o original usou multi-stage build)
