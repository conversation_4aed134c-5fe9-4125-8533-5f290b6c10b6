# AWS Credentials Setup

Este documento explica como configurar as credenciais AWS para o projeto audie-generator-ms.

## Para Produção (EC2) - RECOMENDADO

### 1. Criar IAM Role para EC2

1. No AWS Console, vá para **IAM > Roles**
2. Clique em **Create role**
3. Selecione **AWS service** > **EC2**
4. <PERSON><PERSON><PERSON> as seguintes políticas:
   - `SecretsManagerReadWrite` (ou uma política customizada mais restritiva)
   - Exemplo de política customizada:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
            ],
            "Resource": "arn:aws:secretsmanager:us-east-1:*:secret:prod/audie*"
        }
    ]
}
```

5. Nomeie a role (ex: `audie-generator-ec2-role`)

### 2. Anexar Role à Instância EC2

1. No EC2 Console, selecione sua instância
2. **Actions > Security > Modify IAM role**
3. <PERSON><PERSON><PERSON><PERSON> a role criada (`audie-generator-ec2-role`)

### 3. Configurar Secret no AWS Secrets Manager

1. No AWS Console, vá para **Secrets Manager**
2. Clique em **Store a new secret**
3. Selecione **Other type of secret**
4. Adicione as chaves necessárias:
   ```json
   {
     "OPENAI_API_KEY": "sua-chave-openai",
     "VANNA_API_KEY": "sua-chave-vanna",
     "MYSQL_HOST": "seu-host-mysql",
     "MYSQL_DB": "seu-db",
     "MYSQL_USER": "seu-usuario",
     "MYSQL_PASSWORD": "sua-senha",
     "MYSQL_PORT": "3306",
     "DEEPSEEK_API_KEY": "sua-chave-deepseek",
     "MODEL_SQL": "seu-modelo"
   }
   ```
5. Nomeie o secret como `prod/audie`

## Para Desenvolvimento Local

### Opção 1: AWS CLI (Recomendado)

1. Instale o AWS CLI:
   ```bash
   pip install awscli
   ```

2. Configure suas credenciais:
   ```bash
   aws configure
   ```
   
3. Insira:
   - AWS Access Key ID
   - AWS Secret Access Key
   - Default region: `us-east-1`
   - Default output format: `json`

### Opção 2: Variáveis de Ambiente

```bash
export AWS_ACCESS_KEY_ID=sua-access-key
export AWS_SECRET_ACCESS_KEY=sua-secret-key
export AWS_DEFAULT_REGION=us-east-1
```

### Opção 3: Arquivo de Credenciais

Crie o arquivo `~/.aws/credentials`:
```ini
[default]
aws_access_key_id = sua-access-key
aws_secret_access_key = sua-secret-key
```

E o arquivo `~/.aws/config`:
```ini
[default]
region = us-east-1
```

## Variáveis de Ambiente Opcionais

Você pode sobrescrever as configurações padrão:

```bash
export AWS_SECRET_NAME=prod/audie
export AWS_REGION=us-east-1
```

## Testando a Configuração

Para testar se as credenciais estão funcionando:

```python
import boto3

try:
    client = boto3.client('secretsmanager', region_name='us-east-1')
    response = client.get_secret_value(SecretId='prod/audie')
    print("✅ Credenciais AWS configuradas corretamente!")
except Exception as e:
    print(f"❌ Erro nas credenciais AWS: {e}")
```

## Segurança

### ✅ Boas Práticas:
- Use IAM Roles para EC2 (sem credenciais hardcoded)
- Princípio do menor privilégio nas políticas IAM
- Rotação regular de secrets
- Monitoramento de acesso via CloudTrail

### ❌ Evite:
- Credenciais no código fonte
- Credenciais em variáveis de ambiente em produção
- Políticas IAM muito permissivas
- Compartilhamento de credenciais

## Troubleshooting

### Erro: "Unable to locate credentials"
- Verifique se a IAM role está anexada à instância EC2
- Para desenvolvimento local, configure AWS CLI ou credenciais

### Erro: "Access Denied"
- Verifique se a IAM role/usuário tem permissão para acessar o Secrets Manager
- Confirme o nome correto do secret

### Erro: "Secret not found"
- Verifique se o secret `prod/audie` existe no Secrets Manager
- Confirme a região correta (us-east-1)
