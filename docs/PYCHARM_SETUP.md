# PyCharm Setup Guide - Audie Generator MS

Este documento fornece instruções completas para configurar o PyCharm para o projeto `audie-generator-ms` e contexto essencial para agentes de IA.

## 📋 Contexto do Projeto

### Origem do Código
- **Fonte**: Extraído da imagem Docker `************.dkr.ecr.us-east-1.amazonaws.com/audie-generator-ms:latest`
- **Método**: Extração completa via `docker save` e reconstrução do filesystem
- **Data da Extração**: 2025-08-20
- **Imagem Original**: AWS Lambda Python 3.12 ARM64

### Estrutura do Projeto
```
audie-generator-ms/
├── src/                          # Código principal da aplicação
│   ├── app.py                   # Ponto de entrada da aplicação
│   ├── config.py                # Configurações
│   ├── controllers/             # Controladores da API
│   │   └── completion_controller.py
│   ├── routes/                  # Definições de rotas
│   │   └── routes.py
│   └── services/                # Serviços de negócio
│       ├── messages.py
│       ├── openai_service.py
│       ├── sql_service.py
│       └── vanna_service.py
├── requirements.txt             # Dependências Python
├── Dockerfile.reconstructed     # Dockerfile reconstruído
├── tmp/                        # Arquivos temporários
└── filesystem/                 # Sistema de arquivos extraído (pode ser removido)
```

## 🔧 Configuração do PyCharm

### 1. Configuração do Interpretador Python

#### Opção A: Ambiente Virtual Local
```bash
# Criar ambiente virtual
python3.12 -m venv venv

# Ativar ambiente (Linux/Mac)
source venv/bin/activate

# Instalar dependências
pip install -r requirements.txt
pip install 'uvicorn[standard]'
pip install 'vanna[openai,mysql]'
```

#### Opção B: Docker como Interpretador
- **File → Settings → Project → Python Interpreter**
- **Add Interpreter → Docker**
- **Image**: Use `Dockerfile.reconstructed`
- **Path mappings**: `/var/task` → `{project_root}`

### 2. Configuração de Variáveis de Ambiente

Criar arquivo `.env` na raiz do projeto:
```env
# Banco de Dados
MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com
MYSQL_DB=audit_gold
MYSQL_USER=devdb
MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J
MYSQL_PORT=3306

# APIs
VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8
OPENAI_API_KEY=********************************************************************************************************************************************************************
DEEPSEEK_API_KEY=***********************************

# Configurações
HOME=/tmp
MODEL_SQL=gpt-4.1-mini

# Lambda (para compatibilidade)
LAMBDA_TASK_ROOT=/var/task
LAMBDA_RUNTIME_DIR=/var/runtime
```

### 3. Configuração de Run/Debug

#### Para FastAPI Local
- **Run → Edit Configurations**
- **Add → Python**
- **Script path**: `src/app.py`
- **Environment variables**: Carregar do arquivo `.env`
- **Working directory**: `{project_root}`

#### Para Lambda Local (usando Mangum)
- **Script**: `uvicorn src.app:app --host 0.0.0.0 --port 8000`
- **Environment**: Mesmo `.env` acima

### 4. Configuração do Source Root
- **File → Settings → Project → Project Structure**
- **Mark as Sources**: Marcar pasta `src/` como source root
- Isso permite imports relativos funcionarem corretamente

## 🏗️ Arquitetura da Aplicação

### Tecnologias Principais
- **FastAPI**: Framework web principal
- **Mangum**: Adaptador ASGI para AWS Lambda
- **Vanna**: Framework para geração de SQL com IA
- **OpenAI**: Integração com GPT models
- **SQLAlchemy**: ORM para banco de dados
- **MySQL**: Banco de dados principal

### Fluxo da Aplicação
1. **Entry Point**: `src/app.py` → função `handler()`
2. **Routing**: `src/routes/routes.py` → definições de endpoints
3. **Controllers**: `src/controllers/` → lógica de controle
4. **Services**: `src/services/` → lógica de negócio
5. **Config**: `src/config.py` → configurações centralizadas

### Endpoints Principais (inferidos)
- Geração de SQL via IA (Vanna + OpenAI)
- Execução de queries no MySQL
- Processamento de mensagens/prompts

## 🐛 Debugging e Desenvolvimento

### Pontos de Atenção
1. **Lambda Context**: Aplicação foi projetada para AWS Lambda
2. **Path Mapping**: Código espera estar em `/var/task`
3. **Environment**: Muitas configurações via variáveis de ambiente
4. **Database**: Conexão com RDS MySQL específico

### Logs e Debugging
- **CloudWatch**: Logs originais estão no AWS CloudWatch
- **Local Debugging**: Usar uvicorn para desenvolvimento local
- **Docker Debugging**: Usar container para ambiente idêntico

### Testes
```bash
# Executar aplicação localmente
uvicorn src.app:app --reload --host 0.0.0.0 --port 8000

# Testar endpoint
curl http://localhost:8000/health  # (se existir)
```

## 📚 Dependências Críticas

### Core Dependencies
```
fastapi==0.115.1      # Framework web
pydantic==2.10.6      # Validação de dados
uvicorn==0.34.0       # Servidor ASGI
mangum==0.19.0        # Lambda adapter
openai==1.68.2        # OpenAI API
SQLAlchemy==2.0.41    # ORM
```

### AI/ML Dependencies
- **vanna[openai,mysql]**: Framework principal para SQL generation
- **openai**: Integração com GPT models
- **deepseek**: API alternativa (via DEEPSEEK_API_KEY)

## 🔒 Segurança e Credenciais

### ⚠️ ATENÇÃO: Credenciais Expostas
As credenciais estão hardcoded no Dockerfile original. Para desenvolvimento:

1. **Nunca commitar** o arquivo `.env`
2. **Usar credenciais de desenvolvimento** diferentes
3. **Rotacionar chaves** se necessário
4. **Considerar AWS Secrets Manager** para produção

### Arquivo .gitignore Recomendado
```gitignore
.env
__pycache__/
*.pyc
venv/
.idea/
filesystem/
*.tar
tmp/
```

## 🚀 Deploy e Produção

### Build Docker
```bash
# Build da imagem
docker build -f Dockerfile.reconstructed -t audie-generator-ms .

# Test local
docker run -p 8080:8080 audie-generator-ms
```

### Deploy para AWS Lambda
- **Runtime**: Python 3.12
- **Architecture**: ARM64
- **Handler**: `src.app.handler`
- **Environment**: Configurar variáveis via AWS Console/Terraform

## 📝 Notas para Agentes de IA

### Contexto Importante
1. **Código Extraído**: Este código foi extraído de uma imagem Docker, não de um repositório Git
2. **Sem Histórico**: Não há histórico de commits ou versioning
3. **Produção Ativa**: Esta aplicação estava rodando em produção na AWS
4. **Dependências Específicas**: Versões fixas por compatibilidade com Lambda

### Limitações Conhecidas
- **Sem testes unitários** visíveis no código extraído
- **Documentação limitada** (apenas código fonte)
- **Configuração hardcoded** em muitos lugares
- **Logs** podem estar configurados para CloudWatch

### Recomendações para Desenvolvimento
1. **Criar testes** para funcionalidades críticas
2. **Refatorar configurações** para serem mais flexíveis
3. **Adicionar documentação** de API (OpenAPI/Swagger)
4. **Implementar logging** estruturado
5. **Separar concerns** entre Lambda e desenvolvimento local
