# Prompt para Geração de Documentação Completa

Use este prompt no agente do outro projeto para gerar documentação equivalente:

---

**PROMPT:**

Preciso que você crie uma documentação completa e profissional para este projeto, similar ao padrão de documentação de projetos enterprise. A documentação deve ser organizada em uma pasta `docs/` e incluir os seguintes arquivos:

## 📋 Arquivos de Documentação Necessários

### 1. `docs/PYCHARM_SETUP.md`
Guia completo para configuração do PyCharm incluindo:
- **Contexto do projeto**: Tecnologias, arquitetura, propósito
- **Configuração do interpretador Python**: Virtual env, Docker, ou sistema
- **Configuração de variáveis de ambiente**: Como configurar .env
- **Source roots**: Quais pastas marcar como source
- **Run/Debug configurations**: Como executar e debugar
- **Estrutura do projeto**: Explicação detalhada de cada pasta/arquivo
- **Dependências críticas**: Ordem de instalação e versões importantes
- **Pontos de atenção**: Configurações específicas do projeto

### 2. `docs/AGENT_CONTEXT.md`
Contexto essencial para agentes de IA trabalhando no projeto:
- **🎯 Contexto crítico**: Origem, estado atual, limitações
- **🏗️ Arquitetura técnica**: Stack, fluxo de dados, estrutura
- **🔧 Configuração de desenvolvimento**: Setup essencial, dependências
- **🚨 Pontos de atenção**: Segurança, limitações, cuidados especiais
- **🧪 Testing & Debugging**: Como testar, endpoints, debugging tips
- **📊 Monitoramento**: Métricas importantes, health checks
- **🔄 Workflow recomendado**: Para novos agentes e modificações
- **🎯 Objetivos do projeto**: Funcionalidade principal, casos de uso

### 3. `README.md` (atualizar/criar)
Documentação principal do projeto com:
- **📋 Visão geral**: Descrição, funcionalidades principais
- **🏗️ Arquitetura**: Diagrama/descrição da arquitetura
- **🚀 Quick Start**: Pré-requisitos, configuração rápida, execução
- **📁 Estrutura do projeto**: Tree view com explicações
- **🔧 Configuração**: PyCharm, Docker, variáveis de ambiente
- **🔑 Variáveis de ambiente**: Tabela com todas as variáveis
- **🧪 Desenvolvimento**: Testes, debugging, logs
- **🚀 Deploy**: Instruções de deploy para diferentes ambientes
- **📊 Monitoramento**: Métricas, health checks
- **🔒 Segurança**: Boas práticas, cuidados com credenciais
- **🤝 Contribuição**: Guidelines para desenvolvimento
- **📚 Documentação adicional**: Links para outros docs

### 4. `.env.example`
Template de configuração com:
- Todas as variáveis de ambiente necessárias
- Comentários explicativos para cada seção
- Valores de exemplo (sem credenciais reais)
- Separação por categorias (DB, APIs, Config, etc.)

### 5. `.gitignore` (atualizar/criar)
Arquivo completo incluindo:
- Arquivos de ambiente (.env, .env.local, etc.)
- Python (__pycache__, *.pyc, venv/, etc.)
- IDEs (.idea/, .vscode/, etc.)
- Logs e arquivos temporários
- OS específicos (.DS_Store, Thumbs.db, etc.)
- Arquivos de backup e temporários

## 🎯 Requisitos Específicos

### Análise Prévia
Antes de criar a documentação, faça uma análise completa do projeto:
1. **Identifique a stack tecnológica** (linguagem, frameworks, banco de dados)
2. **Mapeie a estrutura de arquivos** e entenda o propósito de cada pasta
3. **Identifique dependências críticas** (requirements.txt, package.json, etc.)
4. **Encontre configurações** (variáveis de ambiente, configs)
5. **Entenda o fluxo da aplicação** (entry points, rotas, serviços)
6. **Identifique pontos de atenção** (segurança, performance, deploy)

### Estilo de Documentação
- **Profissional e detalhada**: Como documentação enterprise
- **Emojis para organização**: Use emojis para seções (📋, 🔧, 🚀, etc.)
- **Exemplos práticos**: Inclua comandos, código, configurações
- **Warnings importantes**: Use ⚠️ para pontos críticos
- **Checkboxes**: Use listas de verificação quando apropriado
- **Código formatado**: Use blocos de código com syntax highlighting

### Contexto para Agentes de IA
No arquivo `docs/AGENT_CONTEXT.md`, inclua especificamente:
- **Limitações conhecidas** do projeto
- **Pontos de atenção** para modificações
- **Workflow recomendado** para novos agentes
- **Checklist** para nova instância de agente
- **Recursos adicionais** e links úteis

### Organização Final
Após criar todos os arquivos:
1. **Crie a pasta `docs/`** se não existir
2. **Mova arquivos de documentação** para `docs/`
3. **Atualize referências** no README.md para apontar para `docs/`
4. **Verifique links** entre documentos
5. **Teste configurações** descritas na documentação

## ✅ Resultado Esperado

Ao final, o projeto deve ter:
```
projeto/
├── docs/
│   ├── PYCHARM_SETUP.md      # Guia completo PyCharm
│   ├── AGENT_CONTEXT.md      # Contexto para agentes IA
│   └── [outros docs específicos]
├── README.md                 # Documentação principal
├── .env.example             # Template configuração
├── .gitignore              # Arquivos ignorados
└── [arquivos do projeto]
```

**Comece analisando o projeto atual e depois crie toda a documentação seguindo este padrão. Seja detalhado e profissional - esta documentação será usada por desenvolvedores e agentes de IA no futuro.**

---

## 🎯 PROMPT RESUMIDO PARA COPIAR:

Analise este projeto completamente e crie documentação profissional enterprise na pasta `docs/` incluindo:

1. **`docs/PYCHARM_SETUP.md`** - Guia completo configuração PyCharm (contexto, interpretador, env vars, source roots, run/debug, estrutura, dependências, pontos de atenção)

2. **`docs/AGENT_CONTEXT.md`** - Contexto para agentes IA (origem, arquitetura, configuração, pontos de atenção, testing, monitoramento, workflow, objetivos)

3. **`README.md`** - Doc principal (visão geral, arquitetura, quick start, estrutura, configuração, env vars, desenvolvimento, deploy, monitoramento, segurança, contribuição)

4. **`.env.example`** - Template configuração completo com comentários

5. **`.gitignore`** - Arquivo completo (env, Python, IDEs, logs, OS, backups)

Use emojis para organização, seja detalhado, inclua exemplos práticos, marque pontos críticos com ⚠️, e foque em contexto para futuros desenvolvedores e agentes IA. Analise primeiro a stack tecnológica, estrutura, dependências e fluxo da aplicação.
