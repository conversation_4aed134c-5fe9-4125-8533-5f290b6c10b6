# Agent Context - Audie Generator MS

Este documento fornece contexto essencial para agentes de IA trabalhando neste projeto.

## 🎯 Contexto Crítico

### Origem do Projeto
- **IMPORTANTE**: Este código foi **extraído de uma imagem Docker de produção**
- **Não é um repositório Git tradicional** - sem histórico de commits
- **Data da extração**: 2025-08-20
- **Imagem original**: `637423516204.dkr.ecr.us-east-1.amazonaws.com/audie-generator-ms:latest`
- **Ambiente original**: AWS Lambda Python 3.12 ARM64

### Estado Atual do Código
✅ **Disponível**:
- Código fonte completo em `src/`
- Dependências exatas em `requirements.txt`
- Dockerfile reconstruído funcionalmente idêntico
- Configurações de ambiente extraídas

❌ **Não Disponível**:
- Histórico de desenvolvimento
- Testes unitários
- Documentação original
- Comentários do desenvolvedor original
- Configurações de CI/CD

## 🏗️ Arquitetura Técnica

### Stack Principal
```
FastAPI (0.115.1) + Mangum (0.19.0) → AWS Lambda
├── Vanna AI → Geração de SQL via IA
├── OpenAI API → Processamento de linguagem natural  
├── SQLAlchemy (2.0.41) → ORM para MySQL
└── MySQL RDS → Banco de dados de auditoria
```

### Fluxo de Dados
1. **Input**: Requisição HTTP com prompt em linguagem natural
2. **Processing**: Vanna + OpenAI geram SQL correspondente
3. **Execution**: SQLAlchemy executa query no MySQL
4. **Output**: Resultados formatados via FastAPI

### Estrutura de Arquivos
```
src/
├── app.py                    # 🚀 Lambda handler + FastAPI app
├── config.py                 # ⚙️ Configurações centralizadas
├── controllers/              # 🎮 Lógica de controle HTTP
│   └── completion_controller.py
├── routes/                   # 🛣️ Definições de endpoints
│   └── routes.py
└── services/                 # 🔧 Lógica de negócio
    ├── messages.py           # 💬 Processamento de mensagens
    ├── openai_service.py     # 🤖 Integração OpenAI
    ├── sql_service.py        # 🗄️ Operações SQL
    └── vanna_service.py      # 🧠 Integração Vanna AI
```

## 🔧 Configuração de Desenvolvimento

### PyCharm Setup Essencial
1. **Source Root**: Marcar `src/` como source root
2. **Python Interpreter**: Python 3.12+ com dependências instaladas
3. **Environment Variables**: Configurar via `.env` (usar `.env.example`)
4. **Run Configuration**: Entry point em `src/app.py`

### Dependências Críticas
```bash
# Core (ordem importante)
pip install fastapi==0.115.1
pip install pydantic==2.10.6
pip install uvicorn==0.34.0
pip install mangum==0.19.0

# AI/ML
pip install 'vanna[openai,mysql]'
pip install openai==1.68.2

# Database
pip install SQLAlchemy==2.0.41
```

### Variáveis de Ambiente Obrigatórias
```env
# Database (produção - usar com cuidado)
MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com
MYSQL_DB=audit_gold
MYSQL_USER=devdb
MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J

# APIs (produção - rotacionar se necessário)
OPENAI_API_KEY=sk-proj-...
VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8
DEEPSEEK_API_KEY=***********************************

# Config
MODEL_SQL=gpt-4.1-mini
```

## 🚨 Pontos de Atenção

### Segurança
- **⚠️ CREDENCIAIS EXPOSTAS**: Chaves de API estão hardcoded
- **⚠️ BANCO DE PRODUÇÃO**: Configurações apontam para RDS real
- **⚠️ SEM RATE LIMITING**: Pode gerar custos altos de API

### Limitações Técnicas
- **Lambda-specific**: Código otimizado para AWS Lambda
- **Path dependencies**: Espera estrutura `/var/task`
- **Environment-dependent**: Muitas configurações via env vars
- **No error handling**: Tratamento de erro pode ser limitado

### Desenvolvimento Local
```bash
# Executar localmente (simula Lambda)
uvicorn src.app:app --host 0.0.0.0 --port 8000

# Debug mode
uvicorn src.app:app --reload --log-level debug
```

## 🧪 Testing & Debugging

### Endpoints Inferidos
```bash
# Health check (se existir)
GET /health

# SQL Generation (endpoint principal)
POST /generate-sql
{
  "prompt": "Show me all users from last month",
  "context": "audit_gold database"
}

# Query execution (se existir)
POST /execute-query
{
  "sql": "SELECT * FROM users WHERE created_at > '2024-07-01'"
}
```

### Debugging Tips
1. **Logs**: Verificar CloudWatch logs originais se disponível
2. **Database**: Testar conectividade MySQL primeiro
3. **APIs**: Verificar quotas OpenAI/Vanna
4. **Lambda**: Usar SAM local para simular ambiente

## 📊 Monitoramento

### Métricas Importantes
- **API Costs**: OpenAI token usage
- **Database**: Connection pool, query performance
- **Lambda**: Cold starts, execution time
- **Errors**: SQL generation failures, DB timeouts

### Health Checks
```python
# Verificar componentes críticos
- Database connectivity
- OpenAI API availability  
- Vanna service status
- Environment variables
```

## 🔄 Workflow Recomendado

### Para Novos Agentes
1. **Ler este documento** completamente
2. **Configurar PyCharm** seguindo PYCHARM_SETUP.md
3. **Testar ambiente** com dependências
4. **Verificar conectividade** DB e APIs
5. **Executar aplicação** localmente
6. **Entender fluxo** através do código

### Para Modificações
1. **Backup**: Sempre fazer backup antes de mudanças
2. **Environment**: Testar em ambiente isolado primeiro
3. **Dependencies**: Cuidado com versões específicas
4. **Testing**: Criar testes para novas funcionalidades
5. **Documentation**: Atualizar documentação

## 📚 Recursos Adicionais

### Documentação
- **[README.md](README.md)**: Visão geral do projeto
- **[PYCHARM_SETUP.md](PYCHARM_SETUP.md)**: Setup detalhado PyCharm
- **[DOCKER_RECONSTRUCTION.md](DOCKER_RECONSTRUCTION.md)**: Como foi extraído

### Arquivos de Configuração
- **[.env.example](.env.example)**: Template de configuração
- **[requirements.txt](requirements.txt)**: Dependências Python
- **[Dockerfile.reconstructed](Dockerfile.reconstructed)**: Container reconstruído

### APIs Externas
- **Vanna AI**: https://vanna.ai/docs/
- **OpenAI**: https://platform.openai.com/docs/
- **FastAPI**: https://fastapi.tiangolo.com/
- **SQLAlchemy**: https://docs.sqlalchemy.org/

## 🎯 Objetivos do Projeto

### Funcionalidade Principal
**Converter linguagem natural em SQL** para consultas de auditoria:
- Input: "Mostre usuários que fizeram login na última semana"
- Output: `SELECT * FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)`

### Casos de Uso
- Consultas ad-hoc de auditoria
- Relatórios automatizados
- Análise de dados via linguagem natural
- Dashboard queries dinâmicas

### Performance Targets
- **Latência**: < 2s para geração SQL
- **Accuracy**: > 90% SQL válido
- **Availability**: 99.9% uptime
- **Cost**: Otimizar uso de APIs pagas
