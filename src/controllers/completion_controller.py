from src.services.vanna_service import MyVanna

class CompletionController:
    def __init__(self, vanna_instance: MyVanna):
        self.vanna_instance = vanna_instance

    def generate_completion(self, platform: str, conversation: list):
        # Validação ou transformação de dados, se necessário
        
        # Chamada ao serviço
        _, response = self.vanna_instance.generate_completion(
            plataforma=int(platform),
            message_history=conversation
        )

        return response
