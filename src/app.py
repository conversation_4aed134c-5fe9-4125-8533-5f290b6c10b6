from typing import List, Dict, Any
from pydantic import BaseModel
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from mangum import Mangum
from src.routes import routes as completion
import sys
import logging
import json

# Modelos Pydantic para validação de dados
class Message(BaseModel):
    id: int
    sender: str
    text: str

class ConversationRequest(BaseModel):
    conversation: List[Dict[str, Any]]
    username: str
    platform: int

class ConversationResponse(BaseModel):
    message: str
    metadata: Dict[str, Any]

# Inicializa a aplicação FastAPI
app = FastAPI(title="Vanna Microservice")
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class JsonFormatter(logging.Formatter):
    def format(self, record):
        json_record = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "request_id": getattr(record, "request_id", "no_request_id"),
        }
        if record.exc_info:
            json_record["exc_info"] = self.formatException(record.exc_info)
        return json.dumps(json_record)

logger = logging.getLogger()
logger.setLevel(logging.INFO)

handler_sys = logging.StreamHandler(sys.stdout)
handler_sys.setFormatter(JsonFormatter())
logger.addHandler(handler_sys)


# Include Routes
app.include_router(completion.router)

@app.get("/health")
def health_check():
    return {"status": "ok"}

handler = Mangum(app)

