from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from src.services.vanna_service import <PERSON><PERSON><PERSON>
from src.config import Config
from src.controllers.completion_controller import CompletionController

router = APIRouter()

class CompletionRequest(BaseModel):
    platform: int
    conversation: list

# Instância da classe MyVanna usando Config
vanna_instance = MyVanna(config={
    'vanna_api_key': Config.VANNA_API_KEY,
    'model': 'gpt-4o',
})

vanna_instance.connect_to_mysql(
    host=Config.MYSQL_HOST,
    dbname=Config.MYSQL_DB,
    user=Config.MYSQL_USER,
    password=Config.MYSQL_PASSWORD,
    port=Config.MYSQL_PORT
)

# Instância do controller
completion_controller = CompletionController(vanna_instance)

@router.post("/generate-audie-completion")
async def generate_completion_route(request: CompletionRequest):
    try:
        response = completion_controller.generate_completion(
            platform=request.platform,
            conversation=request.conversation,
        )
        return {
            "statusCode": 200,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
            "body": {"response": response},
        }
    except Exception as e:
        return {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type",
            },
            "body": {"error": str(e)},
        }