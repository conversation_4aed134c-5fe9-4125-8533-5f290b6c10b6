from openai import OpenAI
from typing import List, Dict, Any
from src.config import Config
class OpenAIService:
    def __init__(self):
        self.client = OpenAI(base_url="https://api.deepseek.com", api_key=Config.DEEPSEEK_API_KEY)
        self.default_params = {
            "response_format": {"type": "text"},
            "temperature": 0,
            "top_p": 0.1
        }

    def generate_openai_response(self, message: List[Dict[str, str]], model_name: str) -> str:
        client = OpenAI(api_key=Config.OPENAI_API_KEY)
        try:
            response = client.chat.completions.create(
                model=model_name,
                messages=message,
                temperature=0,
                top_p=0
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"Erro ao gerar resposta: {str(e)}")
    
    def generate_response(self, messages: List[Dict[str, str]], model_name: str) -> str:
        """
        Gera uma resposta usando a API do OpenAI.
        
        Args:
            messages (List[Dict[str, str]]): Lista de mensagens no formato do chat
            model_name (str): Nome do modelo a ser utilizado
            
        Returns:
            str: Resposta gerada pelo modelo
        """
        try:
            response = self.client.chat.completions.create(
                model=model_name,
                messages=messages,
                **self.default_params
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"Erro ao gerar resposta: {str(e)}") 