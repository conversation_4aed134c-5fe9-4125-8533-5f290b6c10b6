from datetime import datetime
import time
import logging
import unicodedata
from functools import wraps
from vanna.openai import OpenAI_Chat
from vanna.vannadb import VannaDB_VectorStore
from src.services.messages import Messages
from src.config import Config
from src.services.openai_service import OpenAIService
from enum import Enum
from sqlalchemy import create_engine, text
from contextlib import contextmanager


class Categorias(Enum):
    RECOMENDACOES = (
        "Toda pergunta que fala sobre recomendacoes filtrando na tabela `consolid_chat_feed`"
    )
    FILTRO_SIMPLES = (
        "Perguntas com apenas um tema: use window function com `attribute_value`"
    )
    FILTRO_COMPOSTO = (
        "Perguntas com mais de um tema: utilize duas ou mais CTEs ou window functions com `attribute_value`, por exemplo `WITH atributo1` e `WITH atributo2`, evitando concentrar tudo em um só."
    )
    METRICA_GERAL = (
        "Perguntas sobre métricas agregadas (score geral, total de atendimentos): crie métricas em %, agrege o resultado de result em apda, se tiver filtro, se não calcule direto de total_consolid_criterion"
    )
    METRICA_CRITERIO = (
        "Perguntas sobre métricas por critério (percentuais, contagens ou ranking de cada critério) faça um groupby criterion_label"
    )
    AGENTES = (
        "Perguntas que mencionam agentes específicos ou pedem agrupamento por agentes, use o `agent_name` disponível na tabela agent_performance_data"
    )
    ANALISE_DE_CONVERSAS = (
        "Você deverá apenas replicar a seguinte query, caso necessário use o filtro de data em date_chat. SELECT acm.body, acm.direction, ac.date_chat FROM audit_landzone.audit_chat_messages acm JOIN audit_landzone.audit_chats ac ON ac.id_chat = acm.fk_id_chat WHERE ac.fk_id_platform =? LIMIT 5000;"
    )
    IDS_EXEMPLOS = (
        "Perguntas que mencionam ids específicos USE SELECT external_id FROM audit_gold.consolid_chat_feed_details WHERE criterion_label = ? AND result = ?  AND id_platform = ?; "
    )
    ANALISE_DE_CRITERIOS = (
        "Perguntas que querem aprofundar sobre um critério, entender o comportamento, Você deverá apenas replicar a seguinte query, caso necessário use o filtro de data em date_chat. SELECT acm.body, acm.direction, ac.date_chat FROM audit_landzone.audit_chat_messages acm JOIN audit_landzone.audit_chats ac ON ac.id_chat = acm.fk_id_chat WHERE ac.fk_id_platform =? LIMIT 5000;"
    )
    OUTROS = ""



# Configuração do logger
logger = logging.getLogger(__name__)

def measure_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"Query {func.__name__} executada em {duration:.4f} segundos")
        return result
    return wrapper

def remover_acentos(texto):
    return ''.join(
            c for c in unicodedata.normalize('NFD', texto)
            if unicodedata.category(c) != 'Mn'
        )


class MyVanna(VannaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        start_time = time.time()
        MY_VANNA_MODEL = 'audie-prd'
        VannaDB_VectorStore.__init__(self,vanna_model=MY_VANNA_MODEL, vanna_api_key=Config.VANNA_API_KEY)
        OpenAI_Chat.__init__(self, config={'model': str(Config.MODEL_SQL), 'api_key': str(Config.OPENAI_API_KEY)})
        self.openai_service = OpenAIService()
        execution_time = time.time() - start_time
        logger.info(f"Tempo de inicialização do MyVanna: {execution_time:.2f} segundos")


    @measure_time
    def is_greeting(self, question) -> bool:
        message = [
            self.user_message(Messages.generate_is_greeting_message(question))
        ]
        print(message)
        result = self.openai_service.generate_response(message, "deepseek-chat")
        if 'sim' in result:
            return True
        else:
            return False
        
    @measure_time
    def intention_classification(self, question, id_platform) -> list: 
        filtros = self.run_sql(f"SELECT attribute_label FROM audit_landzone.audit_platform_attributes WHERE fk_id_platform = {id_platform}")  
        str_filtros = filtros.to_string()
        
        message = [
            self.system_message(Messages.system_intention_classification(str_filtros)),
            self.user_message(Messages.user_intention_classification(question))
            
        ]
        result = self.submit_prompt(message)
        list_categorias = result.split(':')[1].strip().split(",")
        
        return list_categorias
    @measure_time
    def generate_subquestions(self, question, contexto_historico) -> str:
        message = [
            self.user_message(Messages.generate_subquestions_message(question, contexto_historico))
        ]
        return self.openai_service.generate_response(message, "deepseek-chat")

    @measure_time
    def generate_audta_no_data_message(self,data,question,context,queries):
        message = [
            self.system_message(Messages.system_message(context, '')),
            self.user_message(Messages.generate_audta_no_data_message(data, question, queries))
        ]
        return self.submit_prompt(message)
    
    @measure_time
    def generate_audta_conv_data_message(self,data,question,context, queries):
        message = [
            self.system_message(Messages.system_message(context, queries)),
            self.user_message(Messages.generate_audta_conv_data_message(data, question))
        ]
        return self.submit_prompt(message)
    
    @measure_time
    def generate_audta_message(self, data, question, context, queries):
        message = [
            self.system_message(Messages.system_message(context, queries)),
            self.user_message(Messages.generate_audta_message(data, question))
        ]
        print("#########################")
        print(message)
        print("#########################")
        return self.submit_prompt(message)

    @measure_time
    def generate_audta_greeting_message(self, question, context):
        message = [
            self.system_message(Messages.system_message(context, '')),
            self.user_message(question)
        ]
        return self.submit_prompt(message)

    @measure_time
    def _filter_history(self, message_history):
        last_three_messages_str = lambda history: "\n".join(f"Usuário {msg['sender']}: {msg['text']}" for msg in history[-6:])
        filtered_history = last_three_messages_str(message_history)
        ultima_mensagem = message_history[-1]['text'] if message_history else ""
        ultima_mensagem =  message_history[-1]["sender"] + ": " + ultima_mensagem
        return filtered_history, ultima_mensagem

    @measure_time
    def _generate_sqls(self, list_of_subquestions, message, plataforma):
        sqls = []
        curdate = datetime.now().strftime("%Y-%m-%d")
        if len(list_of_subquestions) > 1:
            list_of_subquestions[-1] = message

        for question in list_of_subquestions:
            categorias = self.intention_classification(question, plataforma)
            print("Categorias ---->")
            print(categorias)
            for categoria in categorias:
                nome_enum = remover_acentos(categoria).upper().strip()
                try:
                    valor = Categorias[nome_enum].value
                    question += "\n Dica: " + valor
                except KeyError:
                    print(f"Categoria '{categoria}' não encontrada na enum.")
            print(question)
            response = self.generate_sql(
                question=f"{question} Na plataforma: {plataforma}\n" + ('' if plataforma == 19 or plataforma == 22 else "Considere o ano como 2025, Caso não tenha sido informada a data"),
                allow_llm_to_see_data=True
            )
            sqls.append(response)
        return sqls

    @measure_time
    def _execute_sqls(self, sqls):
        data = []
        logger.info(f"Executando {len(sqls)} SQLs")
        
        # Criar engine do SQLAlchemy
        engine = create_engine(
            f'mysql+pymysql://{Config.MYSQL_USER}:{Config.MYSQL_PASSWORD}@{Config.MYSQL_HOST}:{Config.MYSQL_PORT}/{Config.MYSQL_DB}',
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        try:
            for sql in sqls:
                if sql.startswith('"'):
                    sql = sql[1:-1]
                if self.is_sql_valid(sql):
                    try:
                        # Usar context manager para garantir que a conexão seja fechada
                        with engine.connect().execution_options(stream_results=True) as conn:
                            # Executar a query e processar os resultados em chunks
                            result = conn.execute(text(sql))
                            
                            # Processar os resultados em chunks para economizar memória
                            chunk_size = 5000
                            chunk = []
                            
                            for row in result:
                                chunk.append(str(row))
                                if len(chunk) >= chunk_size:
                                    data.append('\n'.join(chunk))
                                    chunk = []
                            
                            # Adicionar o último chunk se houver
                            if chunk:
                                data.append('\n'.join(chunk))
                                
                    except Exception as e:
                        logger.error(f"Erro ao executar SQL: {str(e)}")
                        continue
                        
        finally:
            # Garantir que a engine seja fechada
            engine.dispose()
            
        return data

    @measure_time
    def _is_analytical_question(self,message, context):
        messages = [self.user_message(Messages._is_out_of_bounds_user(message,context))]
        response = self.openai_service.generate_openai_response(messages, 'gpt-4.1-mini')
        if response == "1" or response == '2':
            return True, False
        elif response == "3":
            return True, True
        else: 
            return False, False
        
        
    @measure_time
    def analyse_messages(self, context, question, id_platform):
        sql_base =  "SELECT acm.body, acm.direction, ac.date_chat FROM audit_landzone.audit_chat_messages acm JOIN audit_landzone.audit_chats ac ON ac.id_chat = acm.fk_id_chat WHERE ac.fk_id_platform =" + str(id_platform) + " LIMIT 5000;"

        list_of_subquestions = self.generate_subquestions(question, context).split("\n")
        print("#########################")
        print(list_of_subquestions)
        print("#########################")
        for i in range(len(list_of_subquestions)):
            list_of_subquestions[i] = list_of_subquestions[i] + "\n use como base a seguinte query para extrair as conversas, keep in mind, ela extrai a conversa inteira : " + sql_base
          
        sqls = self._generate_sqls(list_of_subquestions, question, id_platform)
        

        # Executar SQLs
        data = self._execute_sqls(sqls)
        # data.append(vn.run_sql(sql_base).to_string())
        dados = self._build_data_string(data, list_of_subquestions)

        audta_response = self.generate_audta_conv_data_message(dados, question, context, '\n'.join(sqls))
        
        return  audta_response  

    @measure_time
    def _build_data_string(self, data, list_of_subquestions):
        dados = ""
        for d, pergunta in zip(data, list_of_subquestions):
            
            dados += f"Pergunta: {pergunta}\ndados: {d}\n\n"

        if len(dados) > 100_000:
            dados = dados[:130_000]
        return dados

    @measure_time
    def generate_completion(self, plataforma: int, message_history: list):
        # Filtrar histórico
        filtered_history, ultima_mensagem = self._filter_history(message_history)
        message = filtered_history
        dados = ""


        if len(message_history) == 1 and self.is_greeting(ultima_mensagem):
            logger.info("is a greeting")
            return "",self.generate_audta_greeting_message( ultima_mensagem, filtered_history)

        logger.info("not a greeting, proceeding with a subquestion")
        
        analytical,confirmation = self._is_analytical_question(message_history[-1]['text'], filtered_history)
        print("#########################")
        print(len(filtered_history))
        print(analytical, confirmation)
        print("#########################")
        if analytical and not confirmation and len(message_history) < 2: 
            audta_response = "Essa informação não está no painel, mas posso extrair das conversas e, se quiser, cruzar com outras fontes. Pode ser?"
            return "", audta_response
        
        elif (analytical or confirmation) and len(message_history) >= 2:
            print("#########################")
            print("entrou no 2")
            audta_response = self.analyse_messages(filtered_history, message_history[-1]['text'], plataforma)
            return "", audta_response   
           
        elif analytical and confirmation: 
            print("#########################")
            print("entrou no 3")
            audta_response = self.analyse_messages(filtered_history, message_history[-1]['text'], plataforma ) 
            return "", audta_response
            
        # Gerar subperguntas
        subquestions = self.generate_subquestions(ultima_mensagem, filtered_history)
        list_of_subquestions = subquestions.split("\n")
        
        for q in list_of_subquestions:
            if q == '':
                list_of_subquestions.remove(q)
        
        if len(list_of_subquestions) > 1:
            list_of_subquestions.append(ultima_mensagem)
        # Gerar SQLs
        sqls = self._generate_sqls(list_of_subquestions, message_history[-1]['text'], plataforma)

        # Executar SQLs
        data = self._execute_sqls(sqls)
        print("#########################")
        print(data)
        print("#########################")
        if len(data) == 0:
            audta_response = self.generate_audta_no_data_message("No momento, essa informação não está disponível nos dados consolidados da auditoria. No entanto, é possível obtê-la analisando diretamente as conversas. Caso deseje, posso buscar essa informação em outras fontes e interpretá-la para você.?", ultima_mensagem, filtered_history, '\n'.join(sqls))
            return "", audta_response
    # Construir dados com perguntas e respostas
        dados = self._build_data_string(data, list_of_subquestions)
    # Gerar resposta final
        audta_response = self.generate_audta_message(dados, ultima_mensagem, filtered_history, '\n'.join(sqls))
        return "", audta_response
            
    
    def get_sql_prompt(
        self,
        initial_prompt : str,
        question: str,
        question_sql_list: list,
        ddl_list: list,
        doc_list: list,
        **kwargs,
    ):
        """
        Example:
        ```python
        vn.get_sql_prompt(
            question="What are the top 10 customers by sales?",
            question_sql_list=[{"question": "What are the top 10 customers by sales?", "sql": "SELECT * FROM customers ORDER BY sales DESC LIMIT 10"}],
            ddl_list=["CREATE TABLE customers (id INT, name TEXT, sales DECIMAL)"],
            doc_list=["The customers table contains information about customers and their sales."],
        )

        ```

        This method is used to generate a prompt for the LLM to generate SQL.

        Args:
            question (str): The question to generate SQL for.
            question_sql_list (list): A list of questions and their corresponding SQL statements.
            ddl_list (list): A list of DDL statements.
            doc_list (list): A list of documentation.

        Returns:
            any: The prompt for the LLM to generate SQL.
        """

        if initial_prompt is None:
            initial_prompt = f"You are a {self.dialect} expert. " + \
            "Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. "

        initial_prompt = self.add_ddl_to_prompt(
            initial_prompt, ddl_list, max_tokens=self.max_tokens
        )

        if self.static_documentation != "":
            doc_list.append(self.static_documentation)

        initial_prompt = self.add_documentation_to_prompt(
            initial_prompt, doc_list, max_tokens=self.max_tokens
        )

        initial_prompt += (
            "===Response Guidelines \n"
            "1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \n"
            "2. If the provided context is insufficient, please explain why it can't be generated. \n"
            "3. Please use the most relevant table(s). \n"
            "4. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \n"
            f"5. Ensure that the output SQL is {self.dialect}-compliant and executable, and free of syntax errors. \n"
        )

        message_log = [self.system_message(initial_prompt)]

        for example in question_sql_list:
            if example is None:
                print("example is None")
            else:
                if example is not None and "question" in example and "sql" in example:
                    message_log.append(self.user_message(example["question"]))
                    message_log.append(self.assistant_message(example["sql"]))

        message_log.append(self.user_message(question))

        return message_log
    

vn = MyVanna()

@measure_time
def connect_to_database():
    vn.connect_to_mysql(
        host=Config.MYSQL_HOST,
        dbname=Config.MYSQL_DB,
        user=Config.MYSQL_USER,
        password=Config.MYSQL_PASSWORD,
        port=Config.MYSQL_PORT
    )

connect_to_database()