import os
import boto3
import json
import logging

# Configure logging
logger = logging.getLogger(__name__)

def get_secret():
    """
    Retrieve secrets from AWS Secrets Manager.
    Falls back to environment variables if AWS is not available.
    """
    try:
        secret_name = "prod/audie"
        region_name = "us-east-1"

        client = boto3.client("secretsmanager", region_name=region_name)
        resp = client.get_secret_value(SecretId=secret_name)
        secrets = json.loads(resp["SecretString"])

        logger.info("Successfully retrieved secrets from AWS Secrets Manager")
        return secrets
    except Exception as e:
        logger.warning(f"Failed to retrieve secrets from AWS Secrets Manager: {e}")
        logger.info("Falling back to environment variables")
        return {}

# Load secrets from AWS Secrets Manager
_secrets = get_secret()

class Config:
    VANNA_API_KEY = _secrets.get("VANNA_API_KEY") or os.getenv("VANNA_API_KEY")
    MYSQL_HOST = _secrets.get("MYSQL_HOST") or os.getenv("MYSQL_HOST")
    MYSQL_DB = _secrets.get("MYSQL_DB") or os.getenv("MYSQL_DB")
    MYSQL_USER = _secrets.get("MYSQL_USER") or os.getenv("MYSQL_USER")
    MYSQL_PASSWORD = _secrets.get("MYSQL_PASSWORD") or os.getenv("MYSQL_PASSWORD")
    MYSQL_PORT = int(_secrets.get("MYSQL_PORT") or os.getenv("MYSQL_PORT", 3306))
    OPENAI_API_KEY = _secrets.get("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY")
    DEEPSEEK_API_KEY = _secrets.get("DEEPSEEK_API_KEY") or os.getenv("DEEPSEEK_API_KEY")
    MODEL_SQL = _secrets.get("MODEL_SQL") or os.getenv("MODEL_SQL")