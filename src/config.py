import os
import boto3
import json
import logging

# Configure logging
logger = logging.getLogger(__name__)

def get_secret():
    """
    Retrieve secrets from AWS Secrets Manager.

    For Lambda deployment: Uses IAM Role attached to the Lambda function (recommended).
    For local development: Falls back to AWS credentials file or environment variables.
    """
    try:
        secret_name = os.getenv("AWS_SCRT_NAME", "prod/audie")
        region_name = os.getenv("AWS_RGN", "us-east-1")

        # boto3 will automatically use IAM role when running on Lambda
        # For local development, it will use ~/.aws/credentials or environment variables
        client = boto3.client("secretsmanager", region_name=region_name)
        resp = client.get_secret_value(SecretId=secret_name)
        secrets = json.loads(resp["SecretString"])

        logger.info(f"Successfully retrieved secrets from AWS Secrets Manager: {secret_name}")
        return secrets
    except Exception as e:
        logger.warning(f"Failed to retrieve secrets from AWS Secrets Manager: {e}")
        logger.info("Falling back to environment variables")
        return {}

# Load secrets from AWS Secrets Manager
_secrets = get_secret()

class Config:
    VANNA_API_KEY = _secrets.get("VANNA_API_KEY") or os.getenv("VANNA_API_KEY")
    MYSQL_HOST = _secrets.get("MYSQL_HOST") or os.getenv("MYSQL_HOST")
    MYSQL_DB = _secrets.get("MYSQL_DB") or os.getenv("MYSQL_DB")
    MYSQL_USER = _secrets.get("MYSQL_USER") or os.getenv("MYSQL_USER")
    MYSQL_PASSWORD = _secrets.get("MYSQL_PASSWORD") or os.getenv("MYSQL_PASSWORD")
    MYSQL_PORT = int(_secrets.get("MYSQL_PORT") or os.getenv("MYSQL_PORT", 3306))
    OPENAI_API_KEY = _secrets.get("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY")
    DEEPSEEK_API_KEY = _secrets.get("DEEPSEEK_API_KEY") or os.getenv("DEEPSEEK_API_KEY")
    MODEL_SQL = _secrets.get("MODEL_SQL") or os.getenv("MODEL_SQL")