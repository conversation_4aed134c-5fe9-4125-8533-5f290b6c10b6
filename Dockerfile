# Especifica a arquitetura para compatibilidade com AWS Lambda
FROM --platform=linux/amd64 public.ecr.aws/lambda/python:3.12.2025.01.15.17 as build-image
COPY requirements.txt ./tmp/requirements.txt
COPY ./src ./src
RUN pip install 'uvicorn[standard]'
RUN pip install 'vanna[openai,mysql]'
RUN pip install --no-cache-dir -r ./tmp/requirements.txt
RUN chown -R 1000:1000 ./src
USER 1000

ENV HOME=/tmp

CMD ["src.app.handler"]